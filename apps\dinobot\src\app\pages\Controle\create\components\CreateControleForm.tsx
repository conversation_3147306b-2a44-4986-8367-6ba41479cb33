import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FileText, X } from 'lucide-react';
import { useCreateControle } from '../hooks/useCreateControle';
import {
  useCreateControleStore,
  useCreateControleSelectors,
} from '../stores/CreateControle.store';
import { Button, MiniPDFReader } from '@dinobot/components-ui';
import CtrlGenerator from './CtrlGenerator';

const CreateControleForm: React.FC = () => {
  const { t } = useTranslation(['app/mode']);
  const { reset } = useCreateControleStore();
  const subject = 'mathematics'; //useCreateControleSelectors.use.subject();
  const { chapters, getPartsByChapter } = useCreateControle();

  const [pdf] = useState<any>(null);
  const [pdfIsOpen, setPDFIsOpen] = useState<boolean>(true);

  const LoginMode = process.env.REACT_APP_LOGIN_TYPE === 'dinobot';

  useEffect(() => {
    console.log('Subject', subject);
    console.log();
    // reset();
  }, [reset]);

  if (!subject) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dinoBotVibrantBlue"></div>
      </div>
    );
  }

  return (
    <div className="relative w-full min-h-full mb-20 flex justify-center">
      <div className="z-20 mt-4 sm:mt-10">
        <CtrlGenerator chapters={chapters} getParts={getPartsByChapter} />
      </div>

      {LoginMode && (
        <div className="fixed bottom-0 left-0 size-80 sm:size-96">
          <img
            src="/dinobot.svg"
            alt="dinobot"
            className="w-full h-full object-contain"
          />
        </div>
      )}

      <CtrlPdfViewer
        show={pdfIsOpen}
        pdf={pdf}
        onClose={() => setPDFIsOpen(false)}
        onOpen={() => setPDFIsOpen(true)}
      />
    </div>
  );
};

interface PdfViewerProps {
  show: boolean;
  pdf: any;
  onClose: () => void;
  onOpen: () => void;
}

function CtrlPdfViewer({ show, pdf, onClose, onOpen }: PdfViewerProps) {
  if (!pdf || !show) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">PDF Viewer</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <MiniPDFReader file={pdf} />
      </div>
    </div>
  );
}

export default CreateControleForm;
