import { getExo, getPartsByChapter } from '@/lib/training-mode/server'
import { getChapters } from '@/lib/chapters/actions'
import React from 'react'
import ExoModeLayout from './components/exo-mode-layout'
import { getFeatureRoutesByFeatureName } from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'
import { notFound } from 'next/navigation'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'
import { getDomainsByLevel, getDomainByLevelId } from '@/lib/domains/actions'
import { getSubjectsByDomainIdAndLevelId } from '@/lib/exams/actions'

export default async function ExerciseModePage() {
    const locale = await getLocale()
    const featureFlags = await getFeatureRoutesByFeatureName(
        FeatureFlagName.STUDENT_EXERCISE_MODE
    )

    if (Array.isArray(featureFlags) && featureFlags[0] != null) {
        if (featureFlags[0] === 'not-found') notFound()
        redirect({ href: `/${featureFlags[0]}`, locale })
    }

    return (
        <div className="size-full">
            <ExoModeLayout
                getDomains={getDomainsByLevel}
                getChapters={getChapters}
                getParts={getPartsByChapter}
                getExo={getExo}
                getSubjectsByDomainIdAndLevelId={
                    getSubjectsByDomainIdAndLevelId
                }
            />
        </div>
    )
}
