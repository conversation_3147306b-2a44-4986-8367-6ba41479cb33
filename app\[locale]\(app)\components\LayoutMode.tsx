import AvatarFloat from '@/components/avatar/avatar-float'
import Calculator, {
    CalculatorTrigger
} from '@/components/calculater/calculator'
import { Header } from '@/components/header/header'
import { SubHeader } from '@/components/sub-header/sub-header'
import React from 'react'
import StudentSidebar from '../components/student-sidebar'
import { cn } from '@/lib/utils/utils'
import EstablishmentSideBar from '../../(etablisment)/components/establishment-sidebar'
import { auth, getUser, signOut } from '@/auth'
import { Session } from '@/lib/types'
import { getLocale } from 'next-intl/server'
import { redirect } from '@/i18n/routing'
import { User } from '@/prisma/generated/zod/modelSchema/UserSchema'
import UserTypeSchema from '@/prisma/generated/zod/inputTypeSchemas/UserTypeSchema'
type ModeLayoutProps = {
    children: React.ReactNode
}
const LayoutMode = async ({ children }: ModeLayoutProps) => {
    const HEYGEN_AVATAR_KEY = process.env.HEYGEN_AVATAR_KEY || ''
    const HEYGEN_VOICE_ID = process.env.HEYGEN_VOICE_ID || ''
    const LoginMode =
        (process.env.NEXT_PUBLIC_LOGIN_TYPE ?? process.env.LOGIN_TYPE) ===
        'dinobot'
    const session = (await auth()) as Session
    const user = await getUser(session?.user?.email)
    const locale = await getLocale()

    const disconnect = async () => {
        'use server'
        await signOut()
        redirect({ href: '/', locale })
    }
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden relative">
            <div className="bg-dinoBotBlue flex flex-col z-10">
                {user?.type === UserTypeSchema.enum.establishment ? (
                    <EstablishmentSideBar
                        user={user as User}
                        logOut={disconnect}
                    />
                ) : (
                    <StudentSidebar />
                )}
            </div>
            <div className="flex flex-col size-screen grow">
                {/* <LoadingBar /> */}
                <div
                    className={cn(
                        'sticky top-0 z-0 grow',
                        LoginMode ? 'max-h-28' : 'max-h-16'
                    )}
                >
                    <Header loginType={LoginMode} />
                    {LoginMode && <SubHeader />}
                </div>
                <main className="flex flex-col flex-1 bg-muted/50 h-[calc(100vh-115px)] overflow-y-auto app-scroller ">
                    <div
                        className={`relative flex ${LoginMode ? 'h-[calc(100vh_-_theme(spacing.28))]' : 'h-screen'}`}
                    >
                        <AvatarFloat
                            avatarKey={HEYGEN_AVATAR_KEY}
                            voiceId={HEYGEN_VOICE_ID}
                        />
                        {children}
                    </div>
                </main>
                <CalculatorTrigger />
                <Calculator />
            </div>
        </div>
    )
}

export default LayoutMode
