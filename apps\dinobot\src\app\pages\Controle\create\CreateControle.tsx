import { useTranslation } from 'react-i18next';
import { useCreateControle } from './hooks/useCreateControle';
import CreateControleForm from './components/CreateControleForm';
import { useEffect } from 'react';

const CreateControle: React.FC = () => {
  const { t } = useTranslation(['app/mode']);
  const { isLoading, error, checkFeatureFlags } = useCreateControle();

  // Check feature flags on component mount
  useEffect(() => {
    checkFeatureFlags();
  }, [checkFeatureFlags]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dinoBotVibrantBlue"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="size-full">
      <CreateControleForm />
    </div>
  );
};

export default CreateControle;
