// src/pages/mode/create-controle/hooks/useCreateControle.ts
import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import React, { useState } from 'react'
import { useAuthApiClient } from '../../../../../app/contexts/AppContext'
import { Chapter, Part } from '../CreateControle.types'
import { FeatureFlagName } from '@prisma/client'
import Cookies from 'js-cookie'

export const useCreateControle = () => {
  const navigate = useNavigate()
  const authApiClient = useAuthApiClient()

  // Use local state instead of Zustand to avoid store issues
  const [subject, setSubject] = useState<string | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [parts, setParts] = useState<Part[]>([])
  const [isInitialLoading, setIsInitialLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedChapter, setSelectedChapter] = useState<string | null>(null)

  // Get chapters query
  const getChaptersQuery = useQuery({
    queryKey: ['chapters', subject],
    queryFn: async (): Promise<Chapter[]> => {
      if (!subject) return []

      try {
        // Get user level from cookies
        const userCookie = Cookies.get("user")

        const user = userCookie ? JSON.parse(userCookie) : {}

        console.log("🚀 ~ queryFn: ~ userCookie:", user)
        const response = await authApiClient.get(`/api/chapters/domain/${subject}/level/${user.levelId}`, {
          params: { domain: subject, level: user.level }
        })
        return response.data
      } catch (error) {
        console.error('Failed to fetch chapters:', error)
        throw error
      }
    },
    enabled: !!subject
  })

  // Update chapters when query data changes
  React.useEffect(() => {
    if (getChaptersQuery.data) {
      setChapters(getChaptersQuery.data)
      setSelectedChapter(null) // Reset selected chapter
    }
  }, [getChaptersQuery.data])

  // Handle chapters query error
  React.useEffect(() => {
    if (getChaptersQuery.error) {
      setError(getChaptersQuery.error.message || 'Failed to fetch chapters')
    }
  }, [getChaptersQuery.error])

  // Feature flags check mutation
  const checkFeatureFlagsMutation = useMutation({
    mutationFn: async () => {
      const response = await authApiClient.get(`/api/features/routes/${FeatureFlagName.STUDENT_EVALUATION_MODE}`)
      return response.data
    }
  })

  // Initialize app data
  const initializeApp = React.useCallback(async () => {
    setIsInitialLoading(true)
    setError(null)

    try {
      // Check feature flags first
      const featureData = await checkFeatureFlagsMutation.mutateAsync()

      if (Array.isArray(featureData) && featureData[0] != null) {
        if (featureData[0] === 'not-found') {
          setError('Feature not found')
          setIsInitialLoading(false)
          return
        }
        navigate(`/${featureData[0]}`)
        return
      }

      // Try to get subject from multiple sources
      let topic = null

      // 1. Try from cookies
      topic = Cookies.get("topic")
      console.log("🚀 ~ initializeApp ~ topic:", topic)

      // 2. Try from localStorage
      if (!topic) {
        topic = localStorage.getItem('selectedDomain') || localStorage.getItem('topic')
      }

      // 3. Try from URL params
      if (!topic) {
        const urlParams = new URLSearchParams(window.location.search)
        topic = urlParams.get('domain') || urlParams.get('subject')
      }

      // 4. If no topic found, show error
      if (!topic) {
        setError('No domain/subject selected. Please select a domain first.')
        setIsInitialLoading(false)
        return
      }

      // Set the subject (this will trigger chapters query)
      setSubject(topic)
      setIsInitialLoading(false)

    } catch (error: any) {
      setError(error.message || 'Failed to initialize app')
      setIsInitialLoading(false)
    }
  }, [checkFeatureFlagsMutation.mutateAsync, navigate])

  // Get parts by chapter mutation
  const getPartsByChapterMutation = useMutation({
    mutationFn: async (chapterId: string): Promise<Part[]> => {
      const response = await authApiClient.get(`/api/parts/chapter/${chapterId}`)
      return response.data
    },
    onSuccess: (data) => {
      setParts(data)
    },
    onError: (error: any) => {
      console.error('Failed to fetch parts:', error)
      toast.error('Failed to load chapter parts')
    }
  })

  // Submit form mutation
  const submitFormMutation = useMutation({
    mutationFn: async (formData: { chapterId: string, time: Date }) => {
      // Validate form data
      if (!formData.chapterId) {
        throw new Error('Chapter is required')
      }

      if (!formData.time ||
        (formData.time.getHours() === 0 &&
          formData.time.getMinutes() === 0 &&
          formData.time.getSeconds() === 0)) {
        throw new Error('Duration is required')
      }

      return { success: true }
    },
    onSuccess: () => {
      navigate('/controle')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Validation failed')
    }
  })

  // Reset function
  const reset = React.useCallback(() => {
    setSubject(null)
    setChapters([])
    setParts([])
    setIsInitialLoading(false)
    setError(null)
    setSelectedChapter(null)
  }, [])

  return {
    // State
    subject,
    chapters,
    parts,
    isLoading: isInitialLoading || getChaptersQuery.isLoading,
    error: error || getChaptersQuery.error?.message || null,
    selectedChapter,

    // Actions
    initializeApp,
    getPartsByChapter: getPartsByChapterMutation.mutate,
    submitForm: submitFormMutation.mutate,
    setSelectedChapter,
    reset,

    // Loading states
    isSubmitting: submitFormMutation.isPending,
    isLoadingParts: getPartsByChapterMutation.isPending,

    // For compatibility with existing components
    checkFeatureFlags: initializeApp
  }
}
